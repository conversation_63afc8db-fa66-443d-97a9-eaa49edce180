FROM harbor.trscd.com.cn/trs-police/openjdk11:1.0.3

ARG JAR_FILE
#ADD target/${JAR_FILE} /opt/${JAR_FILE}
COPY target/opt/ /opt/
ENV JAR_FILE_NAME=${JAR_FILE}

#发送短信需要引入其他的依赖
#ENTRYPOINT ["/opt/docker-entrypoint.sh"]
# https://openjdk.org/jeps/320
# https://issues.apache.org/jira/browse/CXF-7932
# 因为这里的原因，需要解包运行
CMD ["java", "-cp", "/opt/BOOT-INF/classes:/opt/BOOT-INF/lib/*","-server","-XX:-OmitStackTraceInFastThrow","-XX:+HeapDumpOnOutOfMemoryError","-XX:HeapDumpPath=/opt/log/java_heapdump.hprof","-XX:-UseLargePages","-Xms1G","-Xmx1G", "com.trs.police.ProjectsApplication"]