package com.trs.police.bigscreen.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.bigscreen.domain.dto.JingCheSearchDTO;
import com.trs.police.common.core.vo.GeometryVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class BigScreenJingCheMapperTest {

    @Resource
    private BigScreenJingCheMapper mapper;

    @Test
    void doPageSelect() {
        // 第一个在下面四个点的连线之中
        //104.141212,30.628046 四川省成都市双流区
        //104.194759,30.639860 四川省成都市双流区
        //104.106887,30.709532 四川省成都市双流区
        //104.080113,30.581958 四川省成都市锦江区
        //104.322448,30.544126 四川省成都市龙泉驿区
        JingCheSearchDTO dto = new JingCheSearchDTO();
        dto.setSrid(0);
        GeometryVO geo = new GeometryVO();
        geo.setType("circle");
        dto.setSrid(4326);
        geo.setGeometry("POINT(30.544126 104.322448)");
        var p = new GeometryVO.Properties();
        p.setRadius(17000.0);
        geo.setProperties(p);
        var d = mapper.doPageSelect(
                Page.of(1, 10),
                dto,
                null,
                List.of(geo),
                null
        );
        System.out.println(d.getTotal());

        geo = new GeometryVO();
        geo.setType("polygon");
        dto.setSrid(4326);
        geo.setGeometry("polygon ((30.639860 104.194759, 30.709532 104.106887, 30.581958 104.080113, 30.544126 104.322448, 30.639860 104.194759))");
        d = mapper.doPageSelect(
                Page.of(1, 10),
                dto,
                null,
                List.of(geo),
                null
        );
        System.out.println(d.getTotal());
    }
}